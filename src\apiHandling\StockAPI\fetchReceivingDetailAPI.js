// API function to fetch receiving details
export const fetchReceivingDetail = async (bearerToken, branchId, grId) => {
    try {
        const response = await fetch(
            `https://retailuat.abisibg.com/api/v1/grndetail?BranchId=${branchId}&GRId=${grId}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${bearerToken}`,
                    'Content-Type': 'application/json',
                },
            }
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching receiving detail:', error);
        throw error;
    }
};
