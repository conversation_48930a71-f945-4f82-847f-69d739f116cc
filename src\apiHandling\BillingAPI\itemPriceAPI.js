import axios from 'axios';

export const itemPriceAPI = async (branchId, itemId, bearerToken, tranSubTypeId = 'RETAIL') => {
  const businessDateUrl = `https://retailuat.abisibg.com/api/v1/currentbusinessday?BranchId=${branchId}`;

  try {
    // Step 1: Get the current business date
    const dateResponse = await axios.get(businessDateUrl, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
      },
    });

    const rawDate = dateResponse.data?.[0]?.BusinessDateCode;
    if (!rawDate) {
      throw new Error('Business date not found');
    }

    // Step 2: Format business date to YYYYMMDD
    const formattedDate = new Date(rawDate)
      .toISOString()
      .split('T')[0]
      .replace(/-/g, '');

    // Step 3: Build the new fetch rate API URL
    const priceUrl = `https://retailuat.abisibg.com/api/v1/fetchrate?BusinessDate=${formattedDate}&BranchID=${branchId}&TranSubTypeId=${tranSubTypeId}&ItemID=${itemId}&CustomerID=ALL`;

    // Step 4: Fetch item price
    const priceResponse = await axios.get(priceUrl, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
      },
    });

    // Extract rate from the response array and return as number to maintain compatibility
    const rateData = priceResponse.data;
    if (Array.isArray(rateData) && rateData.length > 0 && rateData[0].rate !== undefined) {
      return parseFloat(rateData[0].rate); // e.g., 237
    } else {
      throw new Error('Rate not found in response');
    }
  } catch (error) {
    console.error('Error fetching item price:', error.message);
    return {
      success: false,
      message: error.response?.data?.message || 'Something went wrong.',
    };
  }
};
