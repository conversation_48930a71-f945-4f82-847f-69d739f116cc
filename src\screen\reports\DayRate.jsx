import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { DataTable } from 'react-native-paper';
import Navbar from '../../components/Navbar';

import { fetchDayRateReport } from '../../apiHandling/ReportAPI/fetchDayRateReportAPI';


import AsyncStorage from '@react-native-async-storage/async-storage';

const DayRateScreen = () => {
  const [dayRateData, setDayRateData] = useState([]);
  const [filteredDayRateData, setFilteredDayRateData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: null, direction: null });

  const loadDayRateData = async () => {
    const bearerToken = await AsyncStorage.getItem('authToken');
    const selectedBranch = await AsyncStorage.getItem('selectedBranch');
    const parsedBranch = JSON.parse(selectedBranch);
    const loginBranchID = parsedBranch.BranchId;
    setIsLoading(true);
    const data = await fetchDayRateReport(bearerToken, loginBranchID); // pass actual token & branch ID
    setDayRateData(data);
    setFilteredDayRateData(data);
    setIsLoading(false);
  };

  useEffect(() => {
    loadDayRateData();
  }, []);

  // Filter day rate data based on search text
  useEffect(() => {
    if (searchText.trim() === '') {
      setFilteredDayRateData(dayRateData);
    } else {
      const filtered = dayRateData.filter(item =>
        item.itemCode.toLowerCase().includes(searchText.toLowerCase()) ||
        item.itemName.toLowerCase().includes(searchText.toLowerCase()) ||
        item.categoryName.toLowerCase().includes(searchText.toLowerCase()) ||
        item.rate.toString().includes(searchText)
      );
      setFilteredDayRateData(filtered);
    }
  }, [searchText, dayRateData]);

  const handleRefresh = () => {
    loadDayRateData();
  };

  // Sorting function
  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });

    const sortedData = [...filteredDayRateData].sort((a, b) => {
      let aValue = a[key];
      let bValue = b[key];

      // Handle null/undefined values
      if (aValue == null) aValue = '';
      if (bValue == null) bValue = '';

      // Check if values are numeric
      const aIsNumeric = !isNaN(aValue) && !isNaN(parseFloat(aValue));
      const bIsNumeric = !isNaN(bValue) && !isNaN(parseFloat(bValue));

      if (aIsNumeric && bIsNumeric) {
        // Numeric comparison
        aValue = parseFloat(aValue);
        bValue = parseFloat(bValue);
      } else {
        // String comparison (case-insensitive)
        aValue = aValue.toString().toLowerCase();
        bValue = bValue.toString().toLowerCase();
      }

      if (direction === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    setFilteredDayRateData(sortedData);
  };

  // Get sort indicator for column headers
  const getSortIndicator = (columnKey) => {
    if (sortConfig.key === columnKey) {
      return sortConfig.direction === 'asc' ? ' ↑' : ' ↓';
    }
    return '';
  };

  return (
    <View style={styles.container}>
      <Navbar />

      {/* Scroll Options */}
      <View style={styles.scrollOptions_container}>
        <View style={styles.scrollOptions_row}>
          {/* Page Title */}
          <TouchableOpacity style={styles.scrollOptions_backContainer}>
            <Text style={styles.scrollOptions_screenTitle}>Day Rate</Text>
          </TouchableOpacity>

          {/* Refresh Button */}
          <View style={styles.scrollOptions_buttonsContainer}>
            <View style={styles.scrollOptions_buttonWrapper}>
              <TouchableOpacity
                style={[styles.scrollOptions_button, { backgroundColor: '#02096A' }]}
                onPress={handleRefresh}
              >
                <Text style={[styles.scrollOptions_buttonText, { color: 'white' }]}>
                  Refresh
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      {/* Main Content ScrollView */}
      <ScrollView style={{ flex: 1 }}>
        <View style={styles.dayRateContainer}>
          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <TextInput
              style={styles.searchInput}
              placeholder="Search by Item Code, Item Name, Category, or Rate..."
              value={searchText}
              onChangeText={setSearchText}
              placeholderTextColor="#666"
            />
          </View>

          {/* Day Rate Table */}
          <View style={styles.tableContainer}>
            <ScrollView horizontal showsHorizontalScrollIndicator={true}>
              <View style={styles.tableWrapper}>
                <DataTable>
                  <DataTable.Header style={styles.tableHeader}>
                    <DataTable.Title
                      style={styles.columnItemCode}
                      onPress={() => handleSort('itemCode')}
                    >
                      <Text style={styles.tableHeaderText}>
                        Item Code{getSortIndicator('itemCode')}
                      </Text>
                    </DataTable.Title>
                    <DataTable.Title
                      style={styles.columnItemName}
                      onPress={() => handleSort('itemName')}
                    >
                      <Text style={styles.tableHeaderText}>
                        Item Name{getSortIndicator('itemName')}
                      </Text>
                    </DataTable.Title>
                    <DataTable.Title
                      style={styles.columnCategory}
                      onPress={() => handleSort('categoryName')}
                    >
                      <Text style={styles.tableHeaderText}>
                        Category{getSortIndicator('categoryName')}
                      </Text>
                    </DataTable.Title>
                    <DataTable.Title
                      style={styles.columnRate}
                      onPress={() => handleSort('rate')}
                    >
                      <Text style={styles.tableHeaderText}>
                        Rate{getSortIndicator('rate')}
                      </Text>
                    </DataTable.Title>
                  </DataTable.Header>

                  {isLoading ? (
                    <DataTable.Row>
                      <DataTable.Cell style={{ flex: 1 }} colSpan={4}>
                        <Text style={styles.cellText}>Loading rates...</Text>
                      </DataTable.Cell>
                    </DataTable.Row>
                  ) : filteredDayRateData.length === 0 ? (
                    <DataTable.Row>
                      <DataTable.Cell style={{ flex: 1 }} colSpan={4}>
                        <Text style={styles.cellText}>No rate data found</Text>
                      </DataTable.Cell>
                    </DataTable.Row>
                  ) : (
                    filteredDayRateData.map((item, index) => (
                      <DataTable.Row key={index}>
                        <DataTable.Cell style={styles.columnItemCode}>
                          <Text style={styles.cellText}>{item.itemCode}</Text>
                        </DataTable.Cell>
                        <DataTable.Cell style={styles.columnItemName}>
                          <Text style={styles.cellText}>{item.itemName}</Text>
                        </DataTable.Cell>
                        <DataTable.Cell style={styles.columnCategory}>
                          <Text style={styles.cellText}>{item.categoryName}</Text>
                        </DataTable.Cell>
                        <DataTable.Cell style={styles.columnRate}>
                          <Text style={styles.cellText}>₹{item.rate.toFixed(2)}</Text>
                        </DataTable.Cell>
                      </DataTable.Row>
                    ))
                  )}
                </DataTable>

              </View>
            </ScrollView>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },

  // Scroll Options Styles
  scrollOptions_container: {
    backgroundColor: '#E6E6E6',
    paddingVertical: 8,
    marginTop: 0,
  },
  scrollOptions_row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  scrollOptions_backContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scrollOptions_screenTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'black',
  },
  scrollOptions_buttonsContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  scrollOptions_buttonWrapper: {
    width: '25%',
    marginHorizontal: 5,
  },
  scrollOptions_button: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  scrollOptions_buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Day Rate Container Styles
  dayRateContainer: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    margin: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },

  // Search Container Styles
  searchContainer: {
    marginBottom: 15,
  },
  searchInput: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    paddingHorizontal: 15,
    fontSize: 16,
    backgroundColor: '#fff',
  },

  // Table Styles
  tableContainer: {
    marginVertical: 15,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 10,
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  tableWrapper: {
    minWidth: '100%',
  },
  tableHeader: {
    backgroundColor: '#02096A',
  },
  tableHeaderText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  cellText: {
    fontSize: 14,
    color: '#333',
  },
  // Dynamic column widths for Day Rate table
  columnItemCode: {
    minWidth: 120,
    maxWidth: 150,
    flex: 0,
  },
  columnItemName: {
    minWidth: 200,
    maxWidth: 300,
    flex: 1,
  },
  columnCategory: {
    minWidth: 150,
    maxWidth: 200,
    flex: 1,
  },
  columnRate: {
    minWidth: 120,
    maxWidth: 150,
    flex: 0,
  },
});

export default DayRateScreen;