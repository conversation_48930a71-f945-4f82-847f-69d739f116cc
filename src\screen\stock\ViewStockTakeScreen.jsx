import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { Dropdown } from 'react-native-element-dropdown';
import { BLEPrinter } from 'react-native-thermal-receipt-printer';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchStockTakeDetail } from '../../apiHandling/StockAPI/fetchStockTakeDetailAPI';

const ViewStockTakeScreen = ({ route }) => {
    const navigation = useNavigation();
    const { stockAdjustId } = route.params;

    // State for stock take data
    const [stockTakeData, setStockTakeData] = useState(null);
    const [branchData, setBranchData] = useState(null);
    const [loading, setLoading] = useState(true);

    // Bluetooth printer state
    const [availablePrinters, setAvailablePrinters] = useState([]);
    const [selectedPrinter, setSelectedPrinter] = useState(null);
    const [isScanning, setIsScanning] = useState(false);
    const [isPrinting, setIsPrinting] = useState(false);

    // Fetch stock take details
    const fetchStockTakeDetails = async () => {
        try {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            // Fetch stock take details
            const stockTakeResponse = await fetchStockTakeDetail(bearerToken, loginBranchID, stockAdjustId);

            // Fetch branch details
            const branchDetailsResponse = await axios.get(
                `https://retailuat.abisibg.com/api/v1/branchdetails`,
                {
                    params: { Branchid: loginBranchID },
                    headers: {
                        Authorization: `Bearer ${bearerToken}`,
                    },
                }
            );

            const stockTakeDetail = stockTakeResponse || {};
            const branchDetail = branchDetailsResponse?.data?.[0] || {};

            setStockTakeData(stockTakeDetail);
            setBranchData(branchDetail);
        } catch (error) {
            console.error('Error fetching stock take details:', error);
            Alert.alert('Error', 'Failed to load stock take details.');
        } finally {
            setLoading(false);
        }
    };

    // Calculate totals
    const calculateTotals = () => {
        if (!stockTakeData?.details) return { totalNos: 0, totalKgs: 0 };
        
        let totalNos = 0;
        let totalKgs = 0;
        
        stockTakeData.details.forEach(item => {
            totalNos += item.Nos || 0;
            totalKgs += item.Kgs || 0;
        });
        
        return { totalNos, totalKgs };
    };

    // Bluetooth printer functions
    const scanForPrinters = async (showErrorAlert = false) => {
        setIsScanning(true);
        try {
            await BLEPrinter.init();
            const devices = await BLEPrinter.getDeviceList();

            const printerDevices = devices.map(device => ({
                label: device.device_name || device.inner_mac_address,
                value: device.inner_mac_address,
                device: device
            }));

            setAvailablePrinters(printerDevices);

            if (printerDevices.length === 0 && showErrorAlert) {
                Alert.alert('No Printers Found', 'No Bluetooth printers found. Please make sure your printer is discoverable and try again.');
            }
        } catch (error) {
            console.error('Error scanning for printers:', error);
            // Only show alert if explicitly requested (e.g., when user clicks refresh)
            if (showErrorAlert) {
                Alert.alert('Error', 'Failed to scan for printers. Please check Bluetooth permissions and try again.');
            }
        }
        setIsScanning(false);
    };

    const printStockTake = async () => {
        if (!selectedPrinter) {
            Alert.alert('No Printer Selected', 'Please select a printer first.');
            return;
        }

        if (!stockTakeData || !branchData) {
            Alert.alert('Error', 'Stock take data not loaded.');
            return;
        }

        setIsPrinting(true);

        try {
            // Connect to printer
            await BLEPrinter.connectPrinter(selectedPrinter);

            // Build stock take content using the tag format
            let receiptContent = '';

            // Header
            receiptContent += '<C>ABIS EXPORTS INDIA PVT LTD\n';
            receiptContent += `${branchData.BranchName || ''}\n`;
            receiptContent += `${branchData.AreaName || ''}\n`;
            receiptContent += `${branchData.PINCODE || ''}\n`;
            receiptContent += 'PhoneNo :\n';
            receiptContent += '<<StockTake>></C>\n';

            const businessDate = stockTakeData.header?.[0]?.BusinessDate
                ? new Date(stockTakeData.header[0].BusinessDate).toLocaleDateString('en-GB')
                : '';

            receiptContent += `Stock Take ID: ${stockTakeData.header[0]?.DocID || stockAdjustId}\n`;
            receiptContent += `Stock Take Date: ${businessDate}\n`;
            receiptContent += `Location :${branchData.BranchId}/${branchData.BranchName}\n`;
            receiptContent += `Printed On : ${new Date().toLocaleDateString('en-GB')} ${new Date().toLocaleTimeString()}\n`;
            receiptContent += '----------------------------------------\n';
            receiptContent += 'ItemCode  Item Name\n';
            receiptContent += '                            NOS      KGS\n';
            receiptContent += '----------------------------------------\n';

            // Items
            const { totalNos, totalKgs } = calculateTotals();
            
            stockTakeData.details?.forEach(item => {
                const nos = item.Nos || 0;
                const kgs = item.Kgs || 0;
                
                receiptContent += `${item.ItemID}  ${item.ItemName}\n`;
                receiptContent += `                            ${nos.toString().padStart(3)}   ${kgs.toFixed(3)}\n`;
            });

            receiptContent += '----------------------------------------\n';
            receiptContent += `TOTAL :                      ${totalNos}  ${totalKgs.toFixed(3)}\n`;
            receiptContent += `Remarks : ${stockTakeData.header?.[0]?.Remarks || ''}\n`;
            receiptContent += '\n\n\n';

            // Print the complete receipt
            await BLEPrinter.printBill(receiptContent);

            Alert.alert('Print Successful', 'The stock take has been printed successfully.');

        } catch (error) {
            console.error('Print error:', error);
            Alert.alert('Print Failed', 'Failed to print stock take: ' + error.message);
        }

        setIsPrinting(false);
    };

    // Load data and printers on component mount (scan printers silently)
    useEffect(() => {
        fetchStockTakeDetails();
        scanForPrinters(false);
    }, []);

    if (loading) {
        return (
            <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
                <ActivityIndicator size="large" color="#0000ff" />
                <Text>Loading stock take details...</Text>
            </View>
        );
    }

    if (!stockTakeData || !stockTakeData.header || !stockTakeData.details) {
        return (
            <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
                <Text>No stock take data found.</Text>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Text style={{ color: 'blue', marginTop: 10 }}>Go Back</Text>
                </TouchableOpacity>
            </View>
        );
    }

    const header = stockTakeData.header[0];
    const { totalNos, totalKgs } = calculateTotals();

    return (
        <View style={styles.container}>
            <ScrollView style={styles.scrollContainer}>
                <Text style={styles.title}>ABIS EXPORTS INDIA PVT LTD</Text>
            <Text style={styles.center}>{branchData?.BranchName || ''}</Text>
            <Text style={styles.center}>{branchData?.AreaName || ''}</Text>
            <Text style={styles.center}>{branchData?.PINCODE || ''}</Text>
            <Text style={styles.center}>PhoneNo :</Text>
            <Text style={styles.center}>{'<<StockTake>>'}</Text>

            <View style={styles.rowSpaceBetween}>
                <Text>Stock Take ID: {header.DocID || stockAdjustId}</Text>
                <Text>Stock Take Date: {header.BusinessDate ? new Date(header.BusinessDate).toLocaleDateString('en-GB') : ''}</Text>
            </View>
            <View style={styles.rowSpaceBetween}>
                <Text>Location: {branchData?.BranchId}/{branchData?.BranchName}</Text>
            </View>
            <View style={styles.rowRight}>
                <Text>Printed On: {new Date().toLocaleDateString('en-GB')} {new Date().toLocaleTimeString()}</Text>
            </View>

            <View style={styles.divider} />

            <Text style={styles.sectionHeader}>Item Details:</Text>
            <View style={styles.table}>
                <View style={styles.tableRow}>
                    <Text style={[styles.cell, styles.itemCodeCell]}>ItemCode</Text>
                    <Text style={[styles.cell, styles.itemNameCell]}>Item Name</Text>
                    <Text style={[styles.cell, styles.nosCell]}>NOS</Text>
                    <Text style={[styles.cell, styles.kgsCell]}>KGS</Text>
                </View>
                {stockTakeData.details.map((item, index) => (
                    <View style={styles.tableRow} key={index}>
                        <Text style={[styles.cell, styles.itemCodeCell]}>{item.ItemID}</Text>
                        <Text style={[styles.cell, styles.itemNameCell]}>{item.ItemName}</Text>
                        <Text style={[styles.cell, styles.nosCell]}>{item.Nos || 0}</Text>
                        <Text style={[styles.cell, styles.kgsCell]}>{(item.Kgs || 0).toFixed(3)}</Text>
                    </View>
                ))}
            </View>

            <View style={styles.divider} />

            <View style={styles.rowSpaceBetween}>
                <Text style={styles.bold}>TOTAL:</Text>
                <Text style={styles.bold}>{totalNos}    {totalKgs.toFixed(3)}</Text>
            </View>

            {header.Remarks && (
                <View style={styles.remarksContainer}>
                    <Text style={styles.bold}>Remarks:</Text>
                    <Text>{header.Remarks}</Text>
                </View>
            )}

            {/* Printer Controls */}
            <View style={styles.printerControlsContainer}>
                <View style={styles.printerRow}>
                    <View style={styles.dropdownContainer}>
                        <Dropdown
                            style={styles.dropdown}
                            placeholderStyle={styles.placeholderStyle}
                            selectedTextStyle={styles.selectedTextStyle}
                            data={availablePrinters}
                            maxHeight={300}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Printer"
                            value={selectedPrinter}
                            onChange={item => {
                                setSelectedPrinter(item.value);
                            }}
                        />
                    </View>

                    <TouchableOpacity
                        style={[styles.button, styles.refreshButton]}
                        onPress={() => scanForPrinters(true)}
                        disabled={isScanning}
                    >
                        {isScanning ? (
                            <ActivityIndicator size="small" color="#fff" />
                        ) : (
                            <Icon name="refresh" size={20} color="#fff" />
                        )}
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.button, styles.printButton]}
                        onPress={printStockTake}
                        disabled={isPrinting || !selectedPrinter}
                    >
                        {isPrinting ? (
                            <ActivityIndicator size="small" color="#fff" />
                        ) : (
                            <>
                                <Icon name="print" size={20} color="#fff" />
                                <Text style={styles.buttonText}>Print</Text>
                            </>
                        )}
                    </TouchableOpacity>
                </View>
            </View>
            </ScrollView>

            {/* Bottom Back Button (following ViewIndentScreen pattern) */}
            <View style={styles.bottomButtonContainer}>
                <TouchableOpacity
                    style={styles.backButton}
                    onPress={() => navigation.goBack()}
                >
                    <Text style={styles.backButtonText}>Back</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default ViewStockTakeScreen;

const styles = StyleSheet.create({
    backButtonContainer: {
        position: 'absolute',
        top: 10,
        left: 10,
        zIndex: 10,
        padding: 8,
    },
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    scrollContainer: {
        padding: 16,
        paddingBottom: 100, // Add space for bottom button
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        textAlign: 'center',
        marginTop: 40,
    },
    center: {
        textAlign: 'center',
        marginVertical: 2,
    },
    rowSpaceBetween: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: 4,
    },
    rowRight: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        marginVertical: 4,
    },
    sectionHeader: {
        marginTop: 16,
        fontWeight: 'bold',
        fontSize: 16,
        marginBottom: 8,
    },
    table: {
        borderWidth: 1,
        borderColor: '#000',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#000',
        minHeight: 40,
        alignItems: 'center',
    },
    cell: {
        padding: 8,
        textAlign: 'center',
        borderRightWidth: 1,
        borderRightColor: '#000',
        fontSize: 12,
    },
    itemCodeCell: {
        flex: 1.5,
        textAlign: 'left',
    },
    itemNameCell: {
        flex: 3,
        textAlign: 'left',
    },
    nosCell: {
        flex: 1,
        textAlign: 'right',
    },
    kgsCell: {
        flex: 1.2,
        textAlign: 'right',
        borderRightWidth: 0,
    },
    bold: {
        fontWeight: 'bold',
        fontSize: 16,
    },
    divider: {
        marginVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#000',
    },
    remarksContainer: {
        marginTop: 16,
        padding: 8,
        backgroundColor: '#f5f5f5',
        borderRadius: 4,
    },
    printerControlsContainer: {
        marginTop: 20,
        paddingTop: 16,
        borderTopWidth: 1,
        borderTopColor: '#ccc',
    },
    printerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 10,
    },
    dropdownContainer: {
        flex: 1,
    },
    dropdown: {
        height: 50,
        borderColor: '#ccc',
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 12,
        backgroundColor: '#fff',
    },
    placeholderStyle: {
        fontSize: 16,
        color: '#999',
    },
    selectedTextStyle: {
        fontSize: 16,
        color: '#000',
    },
    button: {
        height: 50,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 16,
        flexDirection: 'row',
        gap: 8,
    },
    refreshButton: {
        backgroundColor: '#007bff',
        width: 50,
    },
    printButton: {
        backgroundColor: '#28a745',
        minWidth: 80,
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
    // Bottom button styles (following ViewIndentScreen pattern)
    bottomButtonContainer: {
        position: 'absolute',
        bottom: 20,
        left: 20,
        right: 20,
        alignItems: 'center',
    },
    backButton: {
        backgroundColor: '#dc3545',
        paddingVertical: 15,
        paddingHorizontal: 40,
        borderRadius: 10,
        minWidth: 120,
        minHeight: 60,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    backButtonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
});
