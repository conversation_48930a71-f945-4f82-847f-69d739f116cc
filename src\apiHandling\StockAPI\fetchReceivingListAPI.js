// API function to fetch receiving list
export const fetchReceivingList = async (bearerToken, branchId, fromDate, toDate) => {
    try {
        const response = await fetch(
            `https://retailuat.abisibg.com/api/v1/grnlist?BranchId=${branchId}&TransTypeID=GRN&Stage=ALL&FromDate=${fromDate}&ToDate=${toDate}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${bearerToken}`,
                    'Content-Type': 'application/json',
                },
            }
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching receiving list:', error);
        throw error;
    }
};
