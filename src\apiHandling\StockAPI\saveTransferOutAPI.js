import axios from 'axios';

/**
 * Save transfer out data.
 * @param {string} bearerToken
 * @param {object} payload
 * @returns {Promise<object>} API response
 */
export const saveTransferOut = async (bearerToken, payload) => {
  try {
    console.log('📤 Sending Payload to /api/trout2:\n', JSON.stringify(payload, null, 2));

    const response = await axios.post(
      'https://retailuat.abisaio.com:9001/api/trout2',
      payload,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ API Response:\n', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('❌ Error saving transfer out data:', error);
    throw error;
  }
};
