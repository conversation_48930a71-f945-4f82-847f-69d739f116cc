// API function to fetch stock data
export const fetchStock = async (bearerToken, branchId, binId = 'OKBIN', itemStatusId = 'OK', itemCategoryId = 'ALL', channel = 'ALL', itemId = 'ALL', batchNumber = 'ALL') => {
    try {
        const response = await fetch(
            `https://retailuat.abisibg.com/api/v1/fetchstock?BranchID=${branchId}&BinId=${binId}&ItemStatusID=${itemStatusId}&ItemCategoryId=${itemCategoryId}&Channel=${channel}&ItemID=${itemId}&BatchNumber=${batchNumber}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${bearerToken}`,
                    'Content-Type': 'application/json',
                },
            }
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching stock:', error);
        throw error;
    }
};
